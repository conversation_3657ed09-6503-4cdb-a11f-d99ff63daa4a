# 测试仅使用preference存储和UART通信的配置
# 这个配置应该不会编译I2C和SPI相关代码

esphome:
  name: test-preference-uart-only

esp32:
  board: esp32dev

wifi:
  ssid: "test"
  password: "test"

logger:

api:

ota:

uart:
  id: uart_bus
  tx_pin: 1
  rx_pin: 3
  baud_rate: 9600

bl0906_factory:
  id: sensor_bl0906
  communication: uart  # 只使用UART通信，不应该编译SPI代码
  uart_id: uart_bus
  instance_id: 0x906B0001
  calibration:
    enabled: true
    storage_type: preference  # 只使用preference存储，不应该编译I2C代码

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    voltage:
      name: "Voltage"
    frequency:
      name: "Frequency"
    temperature:
      name: "Temperature"
    current_1:
      name: "Current 1"
    power_1:
      name: "Power 1"
    energy_1:
      name: "Energy 1"
