# EEPROM Header Corruption Fix

## Problem Analysis

### Issue Description
The EEPROM header was being corrupted during calibration data save operations. The magic number was being zeroed out, indicating memory corruption.

**Before Fix:**
- Magic: `0x24C0CA02` → `0x00000000` (corrupted)
- Version: `3` → `768` (corrupted)
- Instance count: `0` → `1` (incorrect)

### Root Cause
The issue was caused by **memory layout overflow** in the 24C02 EEPROM (256 bytes):

**Original Layout Calculation:**
- Header size: 20 bytes (EEPROMHeader struct)
- Index area: 4 bytes (1 instance × 4 bytes)
- Instance data: 235 bytes (4 + 77×3 bytes per instance)
- **Total required: 259 bytes**
- **Available: 256 bytes**

**Result:** The instance data overflowed beyond EEPROM capacity and wrapped around to address 0, overwriting the header!

## Solution Implementation

### 1. Enhanced Layout Validation
Added comprehensive bounds checking in `calculate_layout()`:

```cpp
// 验证布局是否合理，防止地址溢出
size_t header_size = sizeof(EEPROMHeader);
size_t index_size = max_instances_ * 4;
size_t instance_data_size = 4 + entries_per_instance_ * 3;
size_t total_required = header_size + index_size + max_instances_ * instance_data_size;

if (total_required > eeprom_size_) {
    ESP_LOGE(TAG, "EEPROM布局错误: 需要%d字节 > 容量%d字节", total_required, eeprom_size_);
    
    // 自动调整entries_per_instance_以适应容量
    size_t available_for_instances = eeprom_size_ - header_size - index_size;
    size_t max_instance_size = available_for_instances / max_instances_;
    entries_per_instance_ = (max_instance_size - 4) / 3;
    
    ESP_LOGW(TAG, "自动调整每实例条目数为: %d", entries_per_instance_);
}
```

### 2. Address Overflow Protection
Enhanced `get_instance_offset()` with bounds checking:

```cpp
uint16_t offset = header_size + index_size + instance_index * instance_data_size;

// 边界检查，防止地址溢出
if (offset + instance_data_size > eeprom_size_) {
    ESP_LOGE(TAG, "实例%d偏移地址溢出: %d + %d > %d", 
             instance_index, offset, instance_data_size, eeprom_size_);
    return 0; // 返回无效偏移
}
```

### 3. Write Operation Safety
Added validation in all write operations:

```cpp
uint16_t offset = get_instance_offset(index);
if (offset == 0) {
    ESP_LOGE(TAG, "无法获取有效的实例偏移地址");
    return false;
}
```

### 4. Enhanced Debugging
Added detailed memory layout logging:

```cpp
ESP_LOGD(TAG, "内存分配: 头部=%d, 索引=%d, 实例数据=%d, 总需求=%d", 
         header_size, index_size, max_instances_ * (4 + entries_per_instance_ * 3), total_required);
ESP_LOGV(TAG, "实例%d偏移地址: 0x%04X (数据大小: %d)", 
         instance_index, offset, instance_data_size);
```

## Fixed Memory Layout for 24C02

**After Fix:**
- Header: 20 bytes (0x00-0x13)
- Index area: 4 bytes (0x14-0x17)
- Available for instance data: 232 bytes
- Max entries per instance: (232-4)/3 = **76 entries** (reduced from 77)
- **Total used: 256 bytes** ✅

## Testing Recommendations

1. **Clear EEPROM** first to remove corrupted data
2. **Initialize** with new layout
3. **Save calibration data** and verify header integrity
4. **Check memory layout** logs for proper allocation

## Benefits

1. **Prevents header corruption** by ensuring no address overflow
2. **Automatic layout adjustment** for different EEPROM sizes
3. **Enhanced error detection** with detailed logging
4. **Robust bounds checking** at multiple levels
5. **Backward compatibility** maintained for larger EEPROMs

## Usage

The fix is transparent to users. The system will:
1. Automatically detect layout issues during initialization
2. Adjust parameters to fit within EEPROM capacity
3. Log detailed information for debugging
4. Prevent any write operations that could cause corruption

This fix ensures reliable calibration data storage without header corruption.
