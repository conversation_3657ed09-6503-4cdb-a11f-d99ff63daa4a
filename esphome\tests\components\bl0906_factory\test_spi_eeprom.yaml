# Test configuration for SPI communication with EEPROM storage
# This configuration should trigger both USE_SPI_COMMUNICATION_ADAPTER and USE_I2C_EEPROM_CALIBRATION macros

esphome:
  name: test-spi-eeprom

esp32:
  board: esp32dev

wifi:
  ssid: "test"
  password: "test"

logger:
  level: DEBUG

api:
ota:

# SPI configuration for BL0906 communication
spi:
  id: spi_bus
  clk_pin: GPIO18
  mosi_pin: GPIO23
  miso_pin: GPIO19

# I2C configuration for EEPROM storage
i2c:
  id: i2c_bus
  sda: GPIO8
  scl: GPIO9
  scan: true

# BL0906 Factory configuration using SPI communication with EEPROM storage
bl0906_factory:
  id: bl0906_device
  communication: spi          # This should define USE_SPI_COMMUNICATION_ADAPTER
  spi_id: spi_bus
  cs_pin: GPIO5
  i2c_id: i2c_bus            # Required for EEPROM storage
  address: 0x50              # EEPROM I2C address
  instance_id: 0x906B0001
  update_interval: 10s
  calibration:
    enabled: true
    storage_type: eeprom     # This should define USE_I2C_EEPROM_CALIBRATION
    eeprom_type: 24c04       # EEPROM type
  initial_calibration:
    - register: 0xA1
      value: 1000
    - register: 0xA2
      value: 1000

# Basic sensors to test functionality
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    voltage:
      name: "Voltage"
    frequency:
      name: "Frequency"
    temperature:
      name: "Temperature"
    current_1:
      name: "Current 1"
    power_1:
      name: "Power 1"
    energy_1:
      name: "Energy 1"

# Calibration numbers for testing
number:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    current_1_gain:
      name: "Current 1 Gain"
    current_2_gain:
      name: "Current 2 Gain"
