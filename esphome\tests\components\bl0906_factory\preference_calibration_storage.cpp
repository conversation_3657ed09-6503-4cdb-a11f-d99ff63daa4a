#include "preference_calibration_storage.h"
#include "esphome/core/application.h"

namespace esphome {
namespace bl0906_factory {

const char *const PreferenceCalibrationStorage::TAG = "bl0906.preference_storage";

PreferenceCalibrationStorage::PreferenceCalibrationStorage() {
}

bool PreferenceCalibrationStorage::init() {
    ESP_LOGD(TAG, "初始化preference校准存储");
    return load_instance_list();
}

std::string PreferenceCalibrationStorage::get_preference_key(uint32_t instance_id) {
    char key[32];
    snprintf(key, sizeof(key), "bl0906_cal_%08X", instance_id);
    return std::string(key);
}

bool PreferenceCalibrationStorage::read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) {
    std::string key = get_preference_key(instance_id);
    
    // 计算需要的数据大小
    size_t max_entries = 64; // 假设最多64个校准条目
    size_t data_size = sizeof(uint16_t) + max_entries * sizeof(CalibrationEntry);
    
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t data[512];
    
    if (!pref.load(&data)) {
        ESP_LOGD(TAG, "实例 0x%08X 的校准数据不存在", instance_id);
        return false;
    }
    
    // 解析数据
    uint16_t entry_count = *reinterpret_cast<uint16_t*>(data);
    if (entry_count > max_entries) {
        ESP_LOGE(TAG, "实例 0x%08X 的条目数量异常: %d", instance_id, entry_count);
        return false;
    }
    
    entries.clear();
    entries.reserve(entry_count);
    
    CalibrationEntry* entry_data = reinterpret_cast<CalibrationEntry*>(data + sizeof(uint16_t));
    for (uint16_t i = 0; i < entry_count; i++) {
        entries.push_back(entry_data[i]);
    }
    
    ESP_LOGD(TAG, "成功读取实例 0x%08X 的 %d 个校准条目", instance_id, entry_count);
    return true;
}

bool PreferenceCalibrationStorage::write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) {
    if (entries.size() > 64) {
        ESP_LOGE(TAG, "校准条目数量过多: %d", entries.size());
        return false;
    }
    
    std::string key = get_preference_key(instance_id);
    
    // 准备数据
    uint8_t data[512];
    uint16_t entry_count = entries.size();
    *reinterpret_cast<uint16_t*>(data) = entry_count;
    
    CalibrationEntry* entry_data = reinterpret_cast<CalibrationEntry*>(data + sizeof(uint16_t));
    for (size_t i = 0; i < entries.size(); i++) {
        entry_data[i] = entries[i];
    }
    
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    if (!pref.save(&data)) {
        ESP_LOGE(TAG, "保存实例 0x%08X 的校准数据失败", instance_id);
        return false;
    }
    
    // 更新实例列表
    auto it = std::find(instance_list_.begin(), instance_list_.end(), instance_id);
    if (it == instance_list_.end()) {
        instance_list_.push_back(instance_id);
        save_instance_list();
    }
    
    ESP_LOGD(TAG, "成功保存实例 0x%08X 的 %d 个校准条目", instance_id, entry_count);
    return true;
}

bool PreferenceCalibrationStorage::delete_instance(uint32_t instance_id) {
    std::string key = get_preference_key(instance_id);
    
    // ESPPreferenceObject 没有 reset() 方法，我们通过保存空数据来"删除"
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t empty_data[512] = {0};
    pref.save(&empty_data);
    
    // 从实例列表中移除
    auto it = std::find(instance_list_.begin(), instance_list_.end(), instance_id);
    if (it != instance_list_.end()) {
        instance_list_.erase(it);
        save_instance_list();
    }
    
    ESP_LOGD(TAG, "删除实例 0x%08X 的校准数据", instance_id);
    return true;
}

bool PreferenceCalibrationStorage::verify() {
    // preference存储由ESP32 NVS保证数据完整性
    return true;
}

bool PreferenceCalibrationStorage::erase() {
    // 删除所有实例
    for (uint32_t instance_id : instance_list_) {
        delete_instance(instance_id);
    }
    instance_list_.clear();
    save_instance_list();
    
    ESP_LOGD(TAG, "清除所有校准数据");
    return true;
}

std::vector<uint32_t> PreferenceCalibrationStorage::get_instance_list() {
    return instance_list_;
}

bool PreferenceCalibrationStorage::save_instance_list() {
    auto pref = global_preferences->make_preference<uint32_t[16]>(fnv1_hash("bl0906_instances"));
    
    uint32_t data[16] = {0};
    size_t count = std::min(instance_list_.size(), size_t(15));
    data[0] = count;
    
    for (size_t i = 0; i < count; i++) {
        data[i + 1] = instance_list_[i];
    }
    
    return pref.save(&data);
}

bool PreferenceCalibrationStorage::load_instance_list() {
    auto pref = global_preferences->make_preference<uint32_t[16]>(fnv1_hash("bl0906_instances"));
    
    uint32_t data[16];
    if (!pref.load(&data)) {
        ESP_LOGD(TAG, "实例列表不存在，创建新列表");
        instance_list_.clear();
        return true;
    }
    
    uint32_t count = data[0];
    if (count > 15) {
        ESP_LOGE(TAG, "实例列表数量异常: %d", count);
        return false;
    }
    
    instance_list_.clear();
    instance_list_.reserve(count);
    
    for (uint32_t i = 0; i < count; i++) {
        instance_list_.push_back(data[i + 1]);
    }
    
    ESP_LOGD(TAG, "加载了 %d 个实例", count);
    return true;
}

}  // namespace bl0906_factory
}  // namespace esphome 