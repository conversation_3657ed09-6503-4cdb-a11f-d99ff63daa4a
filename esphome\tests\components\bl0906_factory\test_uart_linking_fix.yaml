# 测试UART链接和编译修复的配置文件
# 这个配置应该能够正确编译和链接UART通信适配器，解决所有编译错误

esphome:
  name: test-uart-compilation-fix

esp32:
  board: esp32dev

wifi:
  ssid: "test"
  password: "test"

logger:
  level: DEBUG

api:

ota:

uart:
  id: uart_bus
  tx_pin: 1
  rx_pin: 3
  baud_rate: 9600

bl0906_factory:
  id: sensor_bl0906
  communication: uart  # 使用UART通信，应该正确链接
  uart_id: uart_bus
  instance_id: 0x906B0001
  calibration:
    enabled: true
    storage_type: preference  # 使用preference存储

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    voltage:
      name: "Voltage"
    frequency:
      name: "Frequency"
    temperature:
      name: "Temperature"
    current_1:
      name: "Current 1"
    power_1:
      name: "Power 1"
    energy_1:
      name: "Energy 1"
